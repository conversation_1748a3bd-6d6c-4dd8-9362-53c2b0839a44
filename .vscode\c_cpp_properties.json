{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "../include/iec61850/inc", "../include/iec61850/inc_private", "../include/iec61850/common", "../include/iec61850/client", "../include/common", "../include/common/inc", "../include/hal/inc", "../include/mms/inc", "../include/mms/inc_private", "../include/mms/iso_mms/asn1c", "../include/logging", "${BOOST_INCLUDEDIR_VS2017}", "${LIB_PATH}/msvc2017/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x86"}], "version": 4}