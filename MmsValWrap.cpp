#include "MmsValWrap.h"

// Конструктор с параметром
MmsValWrap::MmsValWrap(MmsValue* v) : value(v) {
}

// Перегруженный оператор = для MmsValue*
MmsValWrap& MmsValWrap::operator=(MmsValue* v) {
    if (value != v) {
        if (value) {
            MmsValue_delete(value);
        }
        value = v;
    }
    return *this;
}

// Деструктор
MmsValWrap::~MmsValWrap() {
    if (value) {
        MmsValue_delete(value);
    }
}

// Получение типа MMS значения
MmsType MmsValWrap::getType() {
    return MmsValue_getType(value);
}

// Проверка на null
bool MmsValWrap::isNull() const {
    return value == nullptr;
}

// Проверка на не-null
bool MmsValWrap::isNotNull() const {
    return value != nullptr;
}

// Получение указателя на MmsValue
MmsValue* MmsValWrap::get() {
    return value;
}

// Оператор разыменования
MmsValue* MmsValWrap::operator*() const {
    return value;
}
