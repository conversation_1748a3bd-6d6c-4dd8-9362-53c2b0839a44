{
    "files.associations": {  
        "*.xadv": "xml",
        "*.sqlbook": "sql",
        "*.ndjson": "jsonl",
        "*.dbclient-js": "javascript",
        "memory": "cpp",
        "xstring": "cpp",
        "iosfwd": "cpp",
        "algorithm": "cpp",
        "array": "cpp",
        "atomic": "cpp",
        "bit": "cpp",
        "bitset": "cpp",
        "cctype": "cpp",
        "charconv": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "compare": "cpp",
        "complex": "cpp",
        "concepts": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cwchar": "cpp",
        "deque": "cpp",
        "exception": "cpp",
        "format": "cpp",
        "fstream": "cpp",
        "functional": "cpp",
        "initializer_list": "cpp",
        "ios": "cpp",
        "iostream": "cpp",
        "istream": "cpp",
        "iterator": "cpp",
        "limits": "cpp",
        "list": "cpp",
        "locale": "cpp",
        "map": "cpp",
        "new": "cpp",
        "optional": "cpp",
        "ostream": "cpp",
        "ranges": "cpp",
        "ratio": "cpp",
        "set": "cpp",
        "span": "cpp",
        "sstream": "cpp",
        "stack": "cpp",
        "stdexcept": "cpp",
        "stop_token": "cpp",
        "streambuf": "cpp",
        "string": "cpp",
        "system_error": "cpp",
        "thread": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "typeindex": "cpp",
        "typeinfo": "cpp",
        "unordered_map": "cpp",
        "unordered_set": "cpp",
        "utility": "cpp",
        "variant": "cpp",
        "vector": "cpp",
        "xfacet": "cpp",
        "xhash": "cpp",
        "xiosbase": "cpp",
        "xlocale": "cpp",
        "xlocbuf": "cpp",
        "xlocinfo": "cpp",
        "xlocmes": "cpp",
        "xlocmon": "cpp",
        "xlocnum": "cpp",
        "xloctime": "cpp",
        "xmemory": "cpp",
        "xtr1common": "cpp",
        "xtree": "cpp",
        "xutility": "cpp"
    },
    "files.exclude": {
        "Debug":true,
        "Release":true,
        ".vs":true,
        "**/*.filters":true,
        "**/*.vcxproj":true,
        "**/*.sln":true,
        "**/~*.*":true,
        "**/*.o": true,
        "**/*.obj": true,
        "**/*.lst": true,
        "**/*.map": true,
        "**/*.out": true,
        "**/*.ldm": true,
        "**/*.creator": true,
        "**/*.user": true,
        "**/*.config": true,
        "**/*.files": true,
        "**/*.includes": true,
        "**/*.cflags": true,
        "**/*.cxxflags": true,
        "**/*.ti": true,
        "**/*.d": true,
    },
    "editor.gotoLocation.multipleDeclarations": "peek",
    "editor.gotoLocation.multipleDefinitions": "peek",
    "editor.gotoLocation.multipleImplementations": "peek"
}