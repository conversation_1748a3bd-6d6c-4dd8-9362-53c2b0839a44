#pragma once

#include "iec61850_client.h"

/**
 * @brief RAII wrapper для MmsValue*
 * 
 * Класс обеспечивает автоматическое управление памятью для MmsValue*,
 * предотвращая утечки памяти и двойное освобождение.
 */
class MmsValWrap {
private:
    MmsValue* value = nullptr;

public:
    // Конструкторы
    MmsValWrap() = default;
    explicit MmsValWrap(MmsValue* v);
    
    // Удаляем копирование
    MmsValWrap(const MmsValWrap&) = delete;
    MmsValWrap& operator=(const MmsValWrap&) = delete;

    // Перегруженный оператор = для MmsValue*
    MmsValWrap& operator=(MmsValue* v);

    // Деструктор
    ~MmsValWrap();

    // Методы для работы с типом
    MmsType getType();

    // Методы проверки состояния
    bool isNull() const;
    bool isNotNull() const;

    // Методы доступа
    MmsValue* get();
    
    // Shorthand for get()
    MmsValue* operator*() const;
};
